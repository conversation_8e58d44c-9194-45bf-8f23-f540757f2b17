﻿using AppoMobi.Maui.DrawnUi.Demo.Views.Controls;
using AppoMobi.Models;
using AppoMobi.Xam;
using AppoMobi.Mobile.Views.Camera.Exposure;
using DrawnUi.Camera;

namespace AppoMobi.Main
{
    // ExposureMeter.Ui.cs

    public partial class ExposureMeter : AppScreen
    {
        ExposureCamera? ExposureCamera;

        public override void OnDisposing()
        {
            base.OnDisposing();

            _blinkAnimator?.Dispose();
        }

        public override void OnAppearing()
        {
            base.OnAppearing();

            ExposureCamera?.OnAppearing();
        }

        public override void OnDisappearing()
        {
            base.OnDisappearing();

            ExposureCamera?.OnDisappearing();
        }

        private double wheelWidth;

        #region Display Results

        protected ObservableRangeCollection<string> Results { get; } = new();

        private int totalResults;
        private bool error;

        public bool Error
        {
            get => error;
            set
            {
                if (value == error)
                {
                    return;
                }

                error = value;
                OnPropertyChanged();
            }
        }

        public int TotalResults
        {
            get => totalResults;
            set
            {
                if (value == totalResults)
                {
                    return;
                }

                totalResults = value;
                OnPropertyChanged();
            }
        }

        void ClearResults()
        {
            if (TotalResults > 0)
            {
                TotalResults = 0;
                MainThread.BeginInvokeOnMainThread(() => { Results.Clear(); });
            }
        }

        void AddResult(string iso, string aperture, string shutter)
        {
            var result = $"ISO {iso}, f{aperture}, {shutter}";

            MainThread.BeginInvokeOnMainThread(() =>
            {
                if (ModuleType == ExposureMeteringMode.EXIF)
                {
                    // Это экспонометр, тут не нужно сохранять измерения
                    // Просто измерил и показал в верхнем левом углу без цифры 1.
                    var current = 1;
                    if (TotalResults > 0)
                    {
                        Results.RemoveAt(0);
                    }

                    Results.Add($"{result}");
                    TotalResults = current;
                }
                else
                {
                    var current = TotalResults + 1;
                    if (current > 10)
                    {
                        Results.RemoveAt(0);
                    }

                    Results.Add($"{current} - {result}");
                    TotalResults = current;
                }
            });
        }

        #endregion

        BlinkAnimator _blinkAnimator;
        private SkiaSvg TargetIndicator;

        void SetBlinking(bool state)
        {
            if (TargetIndicator == null)
            {
                return;
            }

            if (state)
            {
                if (_blinkAnimator == null)
                {
                    _blinkAnimator = new BlinkAnimator(TargetIndicator)
                    {
                        Repeat = -1,
                        Speed = 280,
                        ColorsRatio = 0.66,
                        Color1 = Colors.DarkRed,
                        Color2 = Colors.Black,
                        OnUpdated = (value) =>
                        {
                            TargetIndicator.TintColor = _blinkAnimator.CurrentColor;
                        }
                    };
                }

                if (!_blinkAnimator.IsRunning)
                {
                    _blinkAnimator.Start();
                }
            }
            else
            {
                if (_blinkAnimator != null && _blinkAnimator.IsRunning)
                {
                    _blinkAnimator.Stop();
                }
            }

        }

        public ExposureMeter(ExposureMeteringMode mode)
        {
            ModuleType = mode;

            wheelWidth = 90.0;

            ItemsTime = ExposureData.ShutterSpeeds;
            ItemsIso = ExposureData.IsoValues;
            ItemsAperture = ExposureData.ApertureValues;

            SetDefaultValues();

            WheelPicker PickerTime;
            WheelPicker PickerIso;
            WheelPicker PickerAperture;

            HorizontalOptions = LayoutOptions.Fill;
            VerticalOptions = LayoutOptions.Fill;
            Type = LayoutType.Grid;
            RowSpacing = 0;
            RowDefinitions = new RowDefinitionCollection()
            {
                new RowDefinition(new GridLength(1, GridUnitType.Star)),
                new RowDefinition(new GridLength(300, GridUnitType.Absolute)),
                new RowDefinition(new GridLength(Super.Screen.BottomInset, GridUnitType.Absolute)),
            };

#if WINDOWS
            Background =
                new LinearGradientBrush(
                    new GradientStopCollection()
                    {
                        new GradientStop(BackColors.GradientPageFaderStart, 0),
                        new GradientStop(BackColors.GradientPageFaderStartEnd, 1),
                    }, new(0, 0), new(0, 1));
#endif
            Children = new List<SkiaControl>()
            {
#if WINDOWS
                new SkiaImage()
                    {
                        UseCache = SkiaCacheType.Image,
                        Opacity = MauiProgram.WallpaperOpacity,
                        Source = @"Images\back.jpg",
                    }
                    .WithRowSpan(2)
                    .Fill(),
#endif

                new SkiaLayer()
                    {
                        VerticalOptions = LayoutOptions.Fill,
                        Children = new List<SkiaControl>()
                        {
                            //PREVIEW CAM
                            new ExposureCamera() { ZIndex = -1 }.Assign(out ExposureCamera),

#if true//DEBUG
                            //LUX
                            new SkiaMarkdownLabel()
                            {
                                UseCache = SkiaCacheType.Operations,
                                Margin = new(0, 8),
                                HorizontalOptions = LayoutOptions.End,
                                VerticalOptions = LayoutOptions.Center,
                                TextColor = Colors.GreenYellow,
                                Padding = new(4, 2, 8, 2),
                                FontSize = 20,
                                BackgroundColor = Color.Parse("#33000000")
                            }.Observe(this, (me, prop) =>
                            {
                                if (prop.IsEither(nameof(BindingContext), nameof(Lux)))
                                {
                                    me.IsVisible = !string.IsNullOrEmpty(Lux);
                                    me.Text = Lux;
                                }
                            }),
#endif
                            //MODE
                            new SkiaShape()
                            {
                                UseCache = SkiaCacheType.Operations,
                                BackgroundColor = Color.Parse("#66000000"),
                                CornerRadius = 4,
                                HorizontalOptions = LayoutOptions.End,
                                VerticalOptions = LayoutOptions.End,
                                Padding = 4,
                                Margin = 4,
                                Children = new List<SkiaControl>()
                                {
                                    new SkiaLabel() { FontSize = 12, TextColor = Colors.GreenYellow, MaxLines = 1 }
                                        .Observe(this, (me, prop) =>
                                        {
                                            if (prop.IsEither(nameof(BindingContext), nameof(Mode)))
                                            {
                                                me.Text = Mode;
                                            }
                                        })
                                }
                            },

                            //ERROR
                            new SkiaShape()
                            {
                                UseCache = SkiaCacheType.Operations,
                                BackgroundColor = Color.Parse("#66000000"),
                                CornerRadius = 4,
                                HorizontalOptions = LayoutOptions.End,
                                VerticalOptions = LayoutOptions.Start,
                                Padding = 4,
                                Margin = 4,
                                Children = new List<SkiaControl>()
                                {
                                    new SkiaLabel()
                                    {
                                        Text = "ERROR", FontSize = 12, TextColor = Colors.Red, MaxLines = 1
                                    }
                                }
                            }.Observe(this, (me, prop) =>
                            {
                                if (prop.IsEither(nameof(BindingContext), nameof(Error)))
                                {
                                    me.IsVisible = Error;
                                }
                            }),

                            //CROSS
                            new SkiaSvg()
                                {
                                    UseCache = SkiaCacheType.Operations,
                                    SvgString = App.Current.Resources.Get<string>("SvgTarget"),
                                    WidthRequest = 50,
                                    LockRatio = 1,
                                    TintColor = Colors.DarkRed
                                }.Center()
                                .Assign(out TargetIndicator)
                                .Observe(this, (me, prop) =>
                                {
                                    if (prop.IsEither(nameof(BindingContext), nameof(IsBusy)))
                                    {
                                        me.IsVisible = ModuleType == ExposureMeteringMode.Spot;
                                        if (IsBusy)
                                        {
                                            SetBlinking(true);
                                        }
                                        else
                                        {
                                            SetBlinking(false);
                                        }
                                        //me.TintColor = IsBusy ? Colors.DarkRed : Colors.Black; //todo blink!
                                    }
                                }),

                            //BUTTON
                            new SkiaShape()
                            {
                                Opacity = 0.75,
                                BackgroundColor = Color.Parse("#40ffffff"),
                                UseCache = SkiaCacheType.Operations,
                                Type = ShapeType.Circle,
                                WidthRequest = 42,
                                LockRatio = 1,
                                StrokeColor = Colors.Black,
                                StrokeWidth = 2,
                                Padding = 3,
                                VerticalOptions = LayoutOptions.End,
                                HorizontalOptions = LayoutOptions.Center,
                                Margin = new(0, 0, 0, 32),
                                Children = new List<SkiaControl>()
                                {
                                    new SkiaShape() { BackgroundColor = Colors.DarkGray, Type = ShapeType.Circle }
                                        .Fill().Observe(this, (me, prop) =>
                                        {
                                            if (prop.IsEither(nameof(BindingContext), nameof(IsBusy)))
                                            {
                                                me.BackgroundColor = IsBusy ? Colors.DarkRed : Colors.Black;
                                                me.Opacity = this.IsBusy ? 0.33 : 1.0;
                                            }
                                        })
                                }
                            }.OnTapped(async (me) =>
                            {
                                if (!IsBusy)
                                {
                                    try
                                    {
                                        IsBusy = true;
                                        Error = false;
                                        await MeasureBrightnessAsync();
                                    }
                                    catch (Exception e)
                                    {
                                        Super.Log(e);
                                    }
                                    finally
                                    {
                                        IsBusy = false;
                                    }
                                }
                            }),

                            //RESULTS
                            new SkiaShape()
                                {
                                    UseCache = SkiaCacheType.Operations,
                                    BackgroundColor = Color.Parse("#66000000"),
                                    CornerRadius = 4,
                                    Padding = 4,
                                    Margin = 4,
                                    Spacing = 12,
                                    HorizontalOptions = LayoutOptions.Start,
                                    VerticalOptions = LayoutOptions.Start,
                                    Children = new List<SkiaControl>()
                                    {
                                        new SkiaStack()
                                            {
                                                //RecyclingTemplate = RecyclingTemplate.Disabled,
                                                HorizontalOptions = LayoutOptions.Start,
                                                ItemTemplate = new DataTemplate(() =>
                                                {
                                                    var label = new SkiaLabel()
                                                    {
                                                        UseCache = SkiaCacheType.Operations,
                                                        Text = "???",
                                                        FontSize = 12,
                                                        TextColor = Colors.GreenYellow,
                                                        MaxLines = 1
                                                    };
                                                    label.SetBinding(SkiaLabel.TextProperty,
                                                        static (string x) => x); // compiled binding
                                                    return label;
                                                })
                                            }
                                            .Observe(this, (me, prop) =>
                                            {
                                                if (prop.IsEither(nameof(BindingContext), nameof(Results)))
                                                {
                                                    me.ItemsSource = Results;
                                                }
                                            })
                                    }
                                }
                                .Observe(this, (me, prop) =>
                                {
                                    if (prop.IsEither(nameof(BindingContext), nameof(TotalResults)))
                                    {
                                        me.IsVisible = TotalResults > 0;
                                    }
                                })
                                .OnTapped((control) =>
                                {
                                    if (ModuleType == ExposureMeteringMode.EXIF)
                                    {
                                        ClearResults();
                                    }
                                }),

#if DEBUG
                            new SkiaLabelFps()
                            {
                                Margin = new(0, 0, 4, 28),
                                BackgroundColor = Colors.DarkRed,
                                ForceRefresh = false,
                                HorizontalOptions = LayoutOptions.End,
                                Rotation = -45,
                                TextColor = Colors.White,
                                VerticalOptions = LayoutOptions.End
                            }
#endif
                        }
                    }
                    .SetGrid(0, 0),
                new SkiaStack()
                    {
                        HorizontalOptions = LayoutOptions.Fill,
                        HeightRequest = 300,
                        Spacing = 8,
                        VerticalOptions = LayoutOptions.End,
                        Children =
                        {
                            new SkiaRow()
                                {
                                    Margin = new(0, 8, 0, 0),
                                    UseCache = SkiaCacheType.Image,
                                    HorizontalOptions = LayoutOptions.Center,
                                    VerticalOptions = LayoutOptions.Start,
                                    Spacing = 8,
                                    Children = new List<SkiaControl>()
                                    {
                                        // ISO button (always locked, non-clickable)
                                        new SkiaShape()
                                        {
                                            UseCache = SkiaCacheType.Operations,
                                            Type = ShapeType.Rectangle,
                                            WidthRequest = wheelWidth,
                                            CornerRadius = 4,
                                            StrokeWidth = 1,
                                            StrokeColor = Colors.Orange,
                                            BackgroundColor = Colors.Orange.WithAlpha(0.2f),
                                            Children = new List<SkiaControl>()
                                            {
                                                new SkiaMarkdownLabel()
                                                {
                                                    Margin = 2,
                                                    Text = "ISO",
                                                    HorizontalOptions = LayoutOptions.Fill,
                                                    HorizontalTextAlignment = DrawTextAlignment.Center,
                                                    VerticalTextAlignment = TextAlignment.Center,
                                                    LineBreakMode = LineBreakMode.NoWrap,
                                                    MaxLines = 1,
                                                    TextColor = Colors.Orange
                                                }
                                            },
                                        }.Assign(out var btnISO),

                                        CreateLockButton(ResStrings.Aperture, () => IsApertureLocked,
                                                (val) => IsApertureLocked = val)
                                            .Assign(out var btnAperture),

                                        CreateLockButton(ResStrings.Shutter, () => IsShutterLocked,
                                                (val) => IsShutterLocked = val)
                                            .Assign(out var btnShutter),
                                    }
                                }
                                .Initialize((stack) =>
                                {
                                    _lockButtons.Add(btnISO);
                                    _lockButtons.Add(btnAperture);
                                    _lockButtons.Add(btnShutter);
                                }),
                            new SkiaRow()
                            {
                                HorizontalOptions = LayoutOptions.Center,
                                VerticalOptions = LayoutOptions.Fill, //BUG: WHY will not autosize without fill?
                                Children =  
                                {
                                    //ISO
                                    new WheelPicker(7)
                                        {
                                            UseCache = SkiaCacheType.Operations,
                                            HeightRequest = -1,
                                            HorizontalOptions = LayoutOptions.Start,
                                            VerticalOptions = LayoutOptions.Fill,
                                            WidthRequest = wheelWidth,
                                            LinesColor = BackColors.NavBarDropShadow,
                                            TextSelectedColor = TextColors.Result,
                                            TextColor = TextColors.Result,
                                            DataSource = ItemsIso,
                                        }.Assign(out PickerIso)
                                        .ObserveSelf((me, prop) =>
                                        {
                                            if (prop.IsEither(nameof(BindingContext),
                                                    nameof(WheelPicker.SelectedIndex)))
                                            {
                                                IndexIso = me.SelectedIndex;
                                            }
                                        })
                                        .Observe(this, (me, prop) =>
                                        {
                                            if (prop.IsEither(nameof(BindingContext), nameof(IndexIso)))
                                            {
                                                me.SelectedIndex = IndexIso;
                                            }
                                        }),

                                    //APERTURE
                                    new WheelPicker(7)
                                        {
                                            HorizontalOptions = LayoutOptions.Start,
                                            HeightRequest = -1,
                                            UseCache = SkiaCacheType.Operations,
                                            VerticalOptions = LayoutOptions.Fill,
                                            WidthRequest = wheelWidth,
                                            LinesColor = BackColors.NavBarDropShadow,
                                            TextSelectedColor = TextColors.Result,
                                            TextColor = TextColors.Result,
                                            DataSource = ItemsAperture,
                                        }.Assign(out PickerAperture)
                                        .ObserveSelf((me, prop) =>
                                        {
                                            if (prop.IsEither(nameof(BindingContext),
                                                    nameof(WheelPicker.SelectedIndex)))
                                            {
                                                IndexAperture = me.SelectedIndex;
                                            }
                                        })
                                        .Observe(this, (me, prop) =>
                                        {
                                            if (prop.IsEither(nameof(BindingContext), nameof(IndexAperture)))
                                            {
                                                me.SelectedIndex = IndexAperture;
                                            }
                                        }),


                                    //TIME
                                    new WheelPicker(7)
                                        {
                                            HorizontalOptions = LayoutOptions.Start,
                                            HeightRequest = -1,
                                            UseCache = SkiaCacheType.Operations,
                                            VerticalOptions = LayoutOptions.Fill,
                                            WidthRequest = wheelWidth,
                                            LinesColor = BackColors.NavBarDropShadow,
                                            TextSelectedColor = TextColors.Result,
                                            TextColor = TextColors.Result,
                                            DataSource = ItemsTime,
                                        }.Assign(out PickerTime)
                                        .ObserveSelf((me, prop) =>
                                        {
                                            if (prop.IsEither(nameof(BindingContext),
                                                    nameof(WheelPicker.SelectedIndex)))
                                            {
                                                IndexTime = me.SelectedIndex;
                                            }
                                        })
                                        .Observe(this, (me, prop) =>
                                        {
                                            if (prop.IsEither(nameof(BindingContext), nameof(IndexTime)))
                                            {
                                                me.SelectedIndex = IndexTime;
                                            }
                                        }),
                                }
                            }
                        }
                    }
                    .SetGrid(0, 1)
            };

            //PickerAperture.IndexChanged += (sender, index) =>
            //{
            //    IndexAperture = index;
            //};
            //PickerTime.IndexChanged += (sender, index) =>
            //{
            //    IndexTime = index;
            //};
            //PickerIso.IndexChanged += (sender, index) =>
            //{
            //    IndexIso = index;
            //};
        }


        /// <summary>
        /// Creates a lock/unlock button for exposure parameters
        /// </summary>
        private SkiaShape CreateLockButton(string title, Func<bool> getter, Action<bool> setter)
        {
            var button = new SkiaShape()
                {
                    UseCache = SkiaCacheType.Operations,
                    Type = ShapeType.Rectangle,
                    WidthRequest = wheelWidth,
                    //HeightRequest = 36,
                    CornerRadius = 4,
                    StrokeWidth = 1,
                    StrokeColor = getter() ? Colors.Orange : Colors.Gray,
                    BackgroundColor = getter() ? Colors.Orange.WithAlpha(0.2f) : Colors.Transparent,
                    Children = new List<SkiaControl>()
                    {
                        new SkiaMarkdownLabel()
                        {
                            Margin = 2,
                            Text = title,
                            //FontSize = 10,
                            HorizontalOptions = LayoutOptions.Fill,
                            HorizontalTextAlignment = DrawTextAlignment.Center,
                            VerticalTextAlignment = TextAlignment.Center,
                            LineBreakMode = LineBreakMode.NoWrap,
                            MaxLines = 1,
                            TextColor = getter() ? Colors.Orange : Colors.White
                        }
                    }
                }
                .OnTapped((control) =>
                {
                    setter(!getter());
                    UpdateLockButtonsState();
                });

            return button;
        }

        /// <summary>
        /// Updates all lock button visuals when lock states change
        /// </summary>
        private void UpdateLockButtonsState()
        {
            if (_lockButtons.Count >= 3)
            {
                UpdateLockButton(_lockButtons[0], IsISOLocked);
                UpdateLockButton(_lockButtons[1], IsApertureLocked);
                UpdateLockButton(_lockButtons[2], IsShutterLocked);
            }
        }

        private void UpdateLockButton(SkiaShape button, bool isLocked)
        {
            button.StrokeColor = isLocked ? Colors.Orange : Colors.Gray;
            button.BackgroundColor = isLocked ? Colors.Orange.WithAlpha(0.2f) : Colors.Transparent;
            if (button.Children.FirstOrDefault() is SkiaMarkdownLabel label)
            {
                label.TextColor = isLocked ? Colors.Orange : Colors.White;
            }
        }
    }
}
