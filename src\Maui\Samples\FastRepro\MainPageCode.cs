﻿using AppoMobi.Maui.DrawnUi.Demo.Views.Controls;
using DrawnUi.Views;
using Canvas = DrawnUi.Views.Canvas;

namespace Sandbox
{
    public class MainPageCode : BasePageReloadable, IDisposable
    {
        Canvas Canvas;

        protected override void Dispose(bool isDisposing)
        {
            if (isDisposing)
            {
                this.Content = null;
                Canvas?.Dispose();
            }

            base.Dispose(isDisposing);
        }


        public override void Build()
        {
            Canvas?.Dispose();

            Canvas = new Canvas()
            {
                VerticalOptions = LayoutOptions.Fill,
                HorizontalOptions = LayoutOptions.Fill,
                BackgroundColor = Colors.White,
                Gestures = GesturesMode.Enabled,
                Children =
                {
                    new SkiaLayout()
                    {
                        //COLUMN
                        Type = LayoutType.Column,
                        Padding = 16,
                        Spacing = 0,
                        WidthRequest = 200,
                        VerticalOptions = LayoutOptions.Fill,
                        Children = new List<SkiaControl>()
                        {
                            new SkiaShape() //VLine
                            {
                                UseCache = SkiaCacheType.Operations,
                                Margin = new Thickness(32, 0, 0, 32),
                                Type = ShapeType.Circle,
                                WidthRequest = 32,
                                LockRatio = 1,
                                StrokeColor = Colors.Black,
                                StrokeWidth = 2,
                                Padding = 3,
                                BackgroundColor = Colors.IndianRed,
                                HorizontalOptions = LayoutOptions.Center,
                                Children = new List<SkiaControl>()
                                {
                                    new SkiaShape() { BackgroundColor = Colors.DarkGray, Type = ShapeType.Circle }
                                        .Fill()
                                }
                            },

                            //ATTACHED MESSAGE
                            new SkiaLayout()
                            {
                                //WidthRequest = 200,
                                HorizontalOptions = LayoutOptions.Fill,
                                BackgroundColor = Color.FromHex("#11000000"),
                                Spacing = 0,
                                Children = new List<SkiaControl>()
                                {
                                    new SkiaShape()
                                    {
                                        Margin = new Thickness(8, 8, 0, 8),
                                        CornerRadius = 0,
                                        BackgroundColor = Colors.YellowGreen,
                                        HorizontalOptions = LayoutOptions.Start,
                                        WidthRequest = 2,
                                        VerticalOptions = LayoutOptions.Fill,
                                    },

                                    //new SkiaLabel()
                                    //{
                                    //    LineBreakMode = LineBreakMode.TailTruncation,
                                    //    MaxLines = 1,
                                    //    Text = $"Dev",
                                    //    FontSize = 15,
                                    //    HorizontalOptions = LayoutOptions.Fill,
                                    //    Margin = new Thickness(16, 8, 8, 0),
                                    //    TextColor = Colors.Black,
                                    //},

                                    new SkiaLabel()
                                    {
                                        LineBreakMode = LineBreakMode.TailTruncation,
                                        MaxLines = 1,
                                        Text = $"Что чего и как оно, как искать теперь его?",
                                        FontSize = 15,
                                        Margin = new Thickness(16, 28, 8, 8), //TODO BUG 2
                                        TextColor = Colors.Black,
                                    }
                                }
                            },
                            new SkiaLayout()
                            {
                                BackgroundColor = Colors.Green,
                                Spacing = 0,
                                WidthRequest = 200,
                                VerticalOptions = LayoutOptions.Fill,
                            },
                            new SkiaLayout()
                            {
                                HeightRequest = 200,
                                HorizontalOptions = LayoutOptions.Fill,
                                BackgroundColor = Colors.Blue,
                                Children = new List<SkiaControl>()
                                {
                                    new SkiaShape() //VLine
                                    {
                                        UseCache = SkiaCacheType.Image,
                                        Margin = new Thickness(8, 0, 0, 0),
                                        Type = ShapeType.Circle,
                                        WidthRequest = 32,
                                        LockRatio = 1,
                                        StrokeColor = Colors.Black,
                                        StrokeWidth = 2,
                                        Padding = 3,
                                        BackgroundColor = Colors.IndianRed,
                                        Children = new List<SkiaControl>()
                                        {
                                            new SkiaShape() { BackgroundColor = Colors.DarkGray, Type = ShapeType.Circle }
                                                .Fill()
                                        }
                                    },

                                }
                            }
                        }
                    }
                }
            };


            this.Content = Canvas;
        }
    }
}
